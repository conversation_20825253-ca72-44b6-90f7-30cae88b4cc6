<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Persona Profile</title>
</head>
<body>
    <h1>Test Persona Profile Data</h1>
    <p><PERSON><PERSON> tombol di bawah untuk menyimpan data persona profile ke localStorage dan navigasi ke result page.</p>
    
    <button onclick="saveTestData()">Simpan Data Test & Buka Result Page</button>
    <button onclick="clearData()">Hapus Data</button>
    
    <script>
        const personaProfileExample = {
            archetype: "The Analytical Innovator",
            shortSummary: "Anda adalah seorang pemikir analitis dengan kecenderungan investigatif yang kuat dan kreativitas tinggi. Kombinasi antara kecerdasan logis-matematis dan keterbukaan terhadap pengalaman baru membuat Anda unggul dalam memecahkan masalah kompleks dengan pendekatan inovatif.",
            strengthSummary: "Kekuatan utama Anda terletak pada analisis mendalam, kreativitas, dan dorongan kuat untuk belajar hal baru. Ini membuat Anda mampu menghasilkan solusi unik di berbagai situasi kompleks.",
            strengths: [
                "Kemampuan analisis yang tajam",
                "Kreativitas dan inovasi",
                "Keingintahuan intelektual yang tinggi",
                "Kemampuan belajar mandiri yang kuat",
                "Pemikiran sistematis dan terstruktur"
            ],
            weaknessSummary: "Anda cenderung overthinking, perfeksionis, dan kadang kurang sabar menghadapi proses lambat atau bekerja sama dengan orang lain.",
            weaknesses: [
                "Terkadang terlalu perfeksionis",
                "Dapat terjebak dalam overthinking",
                "Kurang sabar dengan proses yang lambat",
                "Kemampuan sosial yang perlu dikembangkan",
                "Kesulitan mendelegasikan tugas"
            ],
            careerRecommendation: [
                {
                    careerName: "Data Scientist",
                    careerProspect: {
                        jobAvailability: "high",
                        salaryPotential: "high",
                        careerProgression: "high",
                        industryGrowth: "super high",
                        skillDevelopment: "super high"
                    }
                },
                {
                    careerName: "Peneliti",
                    careerProspect: {
                        jobAvailability: "moderate",
                        salaryPotential: "moderate",
                        careerProgression: "moderate",
                        industryGrowth: "moderate",
                        skillDevelopment: "high"
                    }
                },
                {
                    careerName: "Pengembang Software",
                    careerProspect: {
                        jobAvailability: "super high",
                        salaryPotential: "high",
                        careerProgression: "high",
                        industryGrowth: "super high",
                        skillDevelopment: "super high"
                    }
                }
            ],
            insights: [
                "Kembangkan keterampilan komunikasi untuk menyampaikan ide kompleks dengan lebih efektif",
                "Latih kemampuan bekerja dalam tim untuk mengimbangi kecenderungan bekerja sendiri",
                "Manfaatkan kekuatan analitis untuk memecahkan masalah sosial",
                "Cari mentor yang dapat membantu mengembangkan keterampilan kepemimpinan",
                "Tetapkan batas waktu untuk menghindari analisis berlebihan"
            ],
            skillSuggestion: [
                "Public Speaking",
                "Leadership",
                "Teamwork",
                "Time Management",
                "Delegation"
            ],
            possiblePitfalls: [
                "Mengisolasi diri dari tim karena terlalu fokus pada analisis individu",
                "Menunda keputusan karena perfeksionisme berlebihan",
                "Kurang membangun jaringan karena terlalu fokus pada teknis"
            ],
            riskTolerance: "moderate",
            workEnvironment: "Lingkungan kerja yang memberikan otonomi intelektual, menghargai inovasi, dan menyediakan tantangan kognitif yang berkelanjutan. Anda berkembang di tempat yang terstruktur namun fleksibel.",
            roleModel: [
                "Marie Curie",
                "Albert Einstein",
                "B.J. Habibie"
            ]
        };

        function saveTestData() {
            // Save persona profile
            localStorage.setItem('personaProfile', JSON.stringify(personaProfileExample));
            
            // Set authentication data
            localStorage.setItem('isLoggedIn', 'true');
            localStorage.setItem('userToken', 'dummy-token');
            localStorage.setItem('userData', JSON.stringify({
                email: '<EMAIL>',
                name: 'Test User'
            }));
            
            alert('Data persona profile berhasil disimpan!');
            
            // Navigate to result page
            window.location.href = 'http://localhost:5173/#result';
        }

        function clearData() {
            localStorage.removeItem('personaProfile');
            localStorage.removeItem('isLoggedIn');
            localStorage.removeItem('userToken');
            localStorage.removeItem('userData');
            alert('Data berhasil dihapus!');
        }
    </script>
</body>
</html>
