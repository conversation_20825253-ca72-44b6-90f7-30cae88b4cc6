@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for PetaTalenta */
@layer base {
  body {
    font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
    margin: 0;
    min-height: 100vh;
    background-color: #f9fafb;
  }

  #app {
    min-height: 100vh;
  }
}

@layer components {
  .btn-primary {
    @apply bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 transition duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500;
  }

  .btn-secondary {
    @apply bg-gray-200 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-300 transition duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500;
  }

  .form-input {
    @apply appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm;
  }

  .card {
    @apply bg-white shadow rounded-lg p-6;
  }

  .progress-bar {
    @apply w-full bg-gray-200 rounded-full h-2;
  }

  .progress-fill {
    @apply bg-indigo-600 h-2 rounded-full transition-all duration-300;
  }
}

@layer utilities {
  .animate-pulse-slow {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
}