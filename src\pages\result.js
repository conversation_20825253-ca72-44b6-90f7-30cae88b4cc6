export function createResultPage() {
  return `
    <div class="min-h-screen bg-gray-50">
      <!-- Navigation -->
      <nav class="bg-white shadow">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="flex justify-between h-16">
            <div class="flex items-center">
              <button onclick="navigateTo('dashboard')" class="text-gray-500 hover:text-gray-700 mr-4">
                ← Dashboard
              </button>
              <h1 class="text-xl font-semibold text-gray-900">Hasil Assessment</h1>
            </div>
            <div class="flex items-center space-x-4">
              <button onclick="downloadResult()" class="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700">
                Download PDF
              </button>
              <button onclick="shareResult()" class="text-gray-500 hover:text-gray-700">
                Bagikan
              </button>
            </div>
          </div>
        </div>
      </nav>

      <!-- Main Content -->
      <div class="max-w-6xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
          <div class="flex items-center justify-between">
            <div>
              <h2 class="text-2xl font-bold text-gray-900">Profil Talenta Anda</h2>
              <p class="text-gray-600 mt-1">Assessment diselesaikan pada: <span id="completion-date"></span></p>
            </div>
            <div class="text-right">
              <div class="text-3xl font-bold text-indigo-600" id="overall-score">85</div>
              <div class="text-sm text-gray-500">Skor Keseluruhan</div>
            </div>
          </div>
        </div>

        <!-- Primary Talent Type -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
          <h3 class="text-xl font-semibold text-gray-900 mb-4">Tipe Talenta Utama</h3>
          <div class="flex items-center space-x-6">
            <div class="w-24 h-24 bg-indigo-100 rounded-full flex items-center justify-center">
              <span class="text-2xl font-bold text-indigo-600" id="talent-icon">🎯</span>
            </div>
            <div class="flex-1">
              <h4 class="text-2xl font-bold text-gray-900" id="talent-type">Strategic Thinker</h4>
              <p class="text-gray-600 mt-2" id="talent-description">
                Anda memiliki kemampuan luar biasa dalam melihat gambaran besar, merencanakan strategi jangka panjang, 
                dan mengidentifikasi peluang yang mungkin terlewat oleh orang lain.
              </p>
            </div>
          </div>
        </div>

        <!-- Competency Breakdown -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Kekuatan Utama</h3>
            <div class="space-y-4">
              <div class="flex items-center justify-between">
                <span class="text-gray-700">Pemikiran Strategis</span>
                <div class="flex items-center space-x-2">
                  <div class="w-24 bg-gray-200 rounded-full h-2">
                    <div class="bg-green-500 h-2 rounded-full" style="width: 90%"></div>
                  </div>
                  <span class="text-sm font-medium text-gray-900">90%</span>
                </div>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-gray-700">Analisis & Problem Solving</span>
                <div class="flex items-center space-x-2">
                  <div class="w-24 bg-gray-200 rounded-full h-2">
                    <div class="bg-green-500 h-2 rounded-full" style="width: 85%"></div>
                  </div>
                  <span class="text-sm font-medium text-gray-900">85%</span>
                </div>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-gray-700">Kepemimpinan</span>
                <div class="flex items-center space-x-2">
                  <div class="w-24 bg-gray-200 rounded-full h-2">
                    <div class="bg-blue-500 h-2 rounded-full" style="width: 80%"></div>
                  </div>
                  <span class="text-sm font-medium text-gray-900">80%</span>
                </div>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-gray-700">Inovasi & Kreativitas</span>
                <div class="flex items-center space-x-2">
                  <div class="w-24 bg-gray-200 rounded-full h-2">
                    <div class="bg-blue-500 h-2 rounded-full" style="width: 75%"></div>
                  </div>
                  <span class="text-sm font-medium text-gray-900">75%</span>
                </div>
              </div>
            </div>
          </div>

          <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Area Pengembangan</h3>
            <div class="space-y-4">
              <div class="flex items-center justify-between">
                <span class="text-gray-700">Komunikasi Interpersonal</span>
                <div class="flex items-center space-x-2">
                  <div class="w-24 bg-gray-200 rounded-full h-2">
                    <div class="bg-yellow-500 h-2 rounded-full" style="width: 65%"></div>
                  </div>
                  <span class="text-sm font-medium text-gray-900">65%</span>
                </div>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-gray-700">Manajemen Tim</span>
                <div class="flex items-center space-x-2">
                  <div class="w-24 bg-gray-200 rounded-full h-2">
                    <div class="bg-yellow-500 h-2 rounded-full" style="width: 60%"></div>
                  </div>
                  <span class="text-sm font-medium text-gray-900">60%</span>
                </div>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-gray-700">Adaptabilitas</span>
                <div class="flex items-center space-x-2">
                  <div class="w-24 bg-gray-200 rounded-full h-2">
                    <div class="bg-orange-500 h-2 rounded-full" style="width: 55%"></div>
                  </div>
                  <span class="text-sm font-medium text-gray-900">55%</span>
                </div>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-gray-700">Manajemen Waktu</span>
                <div class="flex items-center space-x-2">
                  <div class="w-24 bg-gray-200 rounded-full h-2">
                    <div class="bg-orange-500 h-2 rounded-full" style="width: 50%"></div>
                  </div>
                  <span class="text-sm font-medium text-gray-900">50%</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Recommendations -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Rekomendasi Pengembangan</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 class="font-medium text-gray-900 mb-2">Posisi yang Cocok</h4>
              <ul class="space-y-2 text-sm text-gray-600">
                <li class="flex items-center">
                  <span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                  Strategic Planning Manager
                </li>
                <li class="flex items-center">
                  <span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                  Business Development Director
                </li>
                <li class="flex items-center">
                  <span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                  Product Strategy Lead
                </li>
                <li class="flex items-center">
                  <span class="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                  Management Consultant
                </li>
              </ul>
            </div>
            <div>
              <h4 class="font-medium text-gray-900 mb-2">Program Pengembangan</h4>
              <ul class="space-y-2 text-sm text-gray-600">
                <li class="flex items-center">
                  <span class="w-2 h-2 bg-yellow-500 rounded-full mr-2"></span>
                  Leadership Communication Workshop
                </li>
                <li class="flex items-center">
                  <span class="w-2 h-2 bg-yellow-500 rounded-full mr-2"></span>
                  Team Management Certification
                </li>
                <li class="flex items-center">
                  <span class="w-2 h-2 bg-orange-500 rounded-full mr-2"></span>
                  Agile & Change Management
                </li>
                <li class="flex items-center">
                  <span class="w-2 h-2 bg-orange-500 rounded-full mr-2"></span>
                  Time Management Mastery
                </li>
              </ul>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row gap-4">
          <button onclick="retakeAssessment()" 
            class="flex-1 bg-gray-200 text-gray-700 py-3 px-6 rounded-md hover:bg-gray-300 transition duration-200">
            Ulangi Assessment
          </button>
          <button onclick="scheduleConsultation()" 
            class="flex-1 bg-indigo-600 text-white py-3 px-6 rounded-md hover:bg-indigo-700 transition duration-200">
            Jadwalkan Konsultasi
          </button>
        </div>
      </div>
    </div>
  `;
}

export function initResult() {
  // Set completion date
  const completionDate = new Date().toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  const dateElement = document.getElementById('completion-date');
  if (dateElement) {
    dateElement.textContent = completionDate;
  }

  // Load saved assessment data if available
  loadAssessmentData();
}

function loadAssessmentData() {
  try {
    // Try to load from the new assessment results format
    const assessmentResults = localStorage.getItem('assessmentResults');
    const lastSaveResult = localStorage.getItem('lastSaveResult');

    if (assessmentResults) {
      const results = JSON.parse(assessmentResults);
      console.log('Assessment results loaded:', results);

      // Display the results in the UI
      displayAssessmentResults(results);

      // Show save status if available
      if (lastSaveResult) {
        const saveResult = JSON.parse(lastSaveResult);
        showSaveStatus(saveResult);
      }
    } else {
      // Fallback: try to load old format answers
      const answers = localStorage.getItem('assessment3PhaseAnswers');
      if (answers) {
        console.log('Legacy assessment answers found:', JSON.parse(answers));
        // Could recalculate results here if needed
      }
    }
  } catch (error) {
    console.error('Error loading assessment data:', error);
  }
}

function displayAssessmentResults(results) {
  // Update overall score (calculate average of all scores)
  const allScores = [
    ...Object.values(results.riasec || {}),
    ...Object.values(results.ocean || {}),
    ...Object.values(results.viaIs || {})
  ];

  if (allScores.length > 0) {
    const overallScore = Math.round(allScores.reduce((sum, score) => sum + score, 0) / allScores.length);
    const scoreElement = document.getElementById('overall-score');
    if (scoreElement) {
      scoreElement.textContent = overallScore;
    }
  }

  // You can add more detailed result display here
  console.log('Results displayed:', {
    riasec: results.riasec,
    ocean: results.ocean,
    viaIs: results.viaIs
  });
}

function showSaveStatus(saveResult) {
  if (saveResult.fallbackSaved && !saveResult.success) {
    // Show a notification that results were saved locally
    const notification = document.createElement('div');
    notification.className = 'bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4';
    notification.innerHTML = `
      <strong>Perhatian:</strong> Hasil assessment disimpan secara lokal karena masalah koneksi.
      <button onclick="retrySync()" class="underline ml-2">Coba sinkronisasi ulang</button>
    `;

    const container = document.querySelector('.max-w-6xl');
    if (container) {
      container.insertBefore(notification, container.firstChild);
    }
  }
}

export function downloadResult() {
  // Simulate PDF download
  alert('Fitur download PDF akan segera tersedia');
}

export function shareResult() {
  // Simulate sharing functionality
  if (navigator.share) {
    navigator.share({
      title: 'Hasil Assessment Talenta',
      text: 'Lihat hasil assessment talenta saya',
      url: window.location.href
    });
  } else {
    // Fallback for browsers that don't support Web Share API
    const url = window.location.href;
    navigator.clipboard.writeText(url).then(() => {
      alert('Link hasil assessment telah disalin ke clipboard');
    });
  }
}

export function retakeAssessment() {
  if (confirm('Apakah Anda yakin ingin mengulang assessment? Hasil sebelumnya akan ditimpa.')) {
    // Clear all assessment data
    localStorage.removeItem('assessmentAnswers');
    localStorage.removeItem('assessmentCompleted');
    localStorage.removeItem('assessmentResultReady');
    localStorage.removeItem('assessment3PhaseAnswers');
    localStorage.removeItem('assessment3PhaseCompleted');
    localStorage.removeItem('assessment3PhaseState');
    localStorage.removeItem('assessmentResults');
    localStorage.removeItem('lastSaveResult');
    localStorage.removeItem('lastAssessmentId');

    // Navigate to assessment
    navigateTo('assessment');
  }
}

// Function to retry syncing local results to server
export function retrySync() {
  import('../services/assessmentService.js').then(({ assessmentService }) => {
    assessmentService.retrySaveAssessment().then(result => {
      if (result.success) {
        alert('Sinkronisasi berhasil! Hasil assessment telah disimpan ke server.');
        // Reload the page to update the display
        location.reload();
      } else {
        alert('Sinkronisasi gagal: ' + result.message);
      }
    }).catch(error => {
      console.error('Retry sync error:', error);
      alert('Terjadi kesalahan saat sinkronisasi.');
    });
  });
}

export function scheduleConsultation() {
  alert('Fitur penjadwalan konsultasi akan segera tersedia');
}
