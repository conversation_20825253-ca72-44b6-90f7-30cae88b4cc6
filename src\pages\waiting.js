export function createWaitingPage() {
  return `
    <div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div class="sm:mx-auto sm:w-full sm:max-w-md">
        <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10 text-center">
          <!-- Loading Animation -->
          <div class="mb-6">
            <div class="mx-auto w-16 h-16 border-4 border-indigo-200 border-t-indigo-600 rounded-full animate-spin"></div>
          </div>
          
          <h2 class="text-2xl font-bold text-gray-900 mb-4">
            Sedang Memproses Assessment Anda
          </h2>
          
          <p class="text-gray-600 mb-6">
            Kami sedang menganalisis jawaban Anda untuk memberikan hasil yang akurat. 
            Proses ini biasanya memakan waktu beberapa menit.
          </p>
          
          <!-- Progress Steps -->
          <div class="mb-8">
            <div class="flex items-center justify-center space-x-4">
              <div class="flex flex-col items-center">
                <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mb-2">
                  <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                  </svg>
                </div>
                <span class="text-xs text-gray-500">Assessment Selesai</span>
              </div>
              
              <div class="w-8 h-1 bg-indigo-200"></div>
              
              <div class="flex flex-col items-center">
                <div class="w-8 h-8 bg-indigo-500 rounded-full flex items-center justify-center mb-2">
                  <div class="w-3 h-3 bg-white rounded-full animate-pulse"></div>
                </div>
                <span class="text-xs text-gray-500">Analisis Data</span>
              </div>
              
              <div class="w-8 h-1 bg-gray-200"></div>
              
              <div class="flex flex-col items-center">
                <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center mb-2">
                  <span class="text-xs text-gray-500">3</span>
                </div>
                <span class="text-xs text-gray-500">Hasil Siap</span>
              </div>
            </div>
          </div>
          
          <!-- Estimated Time -->
          <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <div class="flex items-center">
              <svg class="w-5 h-5 text-blue-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
              </svg>
              <span class="text-sm text-blue-700">
                Estimasi waktu: <span id="estimated-time">2-3 menit</span>
              </span>
            </div>
          </div>
          
          <!-- Tips while waiting -->
          <div class="text-left bg-gray-50 rounded-lg p-4 mb-6">
            <h4 class="font-medium text-gray-900 mb-2">Tahukah Anda?</h4>
            <p class="text-sm text-gray-600" id="waiting-tip">
              Assessment talenta dapat membantu Anda memahami kekuatan dan area pengembangan diri.
            </p>
          </div>
          
          <!-- Action Buttons -->
          <div class="space-y-3">
            <button onclick="checkResults()" 
              class="w-full bg-indigo-600 text-white py-2 px-4 rounded-md hover:bg-indigo-700 transition duration-200">
              Cek Status
            </button>
            
            <button onclick="navigateTo('dashboard')" 
              class="w-full bg-gray-200 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-300 transition duration-200">
              Kembali ke Dashboard
            </button>
          </div>
          
          <!-- Auto-refresh notice -->
          <p class="text-xs text-gray-500 mt-4">
            Halaman ini akan otomatis memperbarui status setiap 30 detik
          </p>
        </div>
      </div>
    </div>
  `;
}

// Tips to show while waiting
const waitingTips = [
  "Assessment talenta dapat membantu Anda memahami kekuatan dan area pengembangan diri.",
  "Hasil assessment akan memberikan insight tentang gaya kerja dan preferensi karir Anda.",
  "Pemetaan talenta membantu organisasi menempatkan orang yang tepat di posisi yang tepat.",
  "Assessment ini menggunakan metodologi yang telah terbukti secara ilmiah.",
  "Hasil Anda akan dibandingkan dengan database profil talenta yang luas."
];

let tipIndex = 0;
let waitingTimer;
let autoRefreshTimer;

export function initWaiting() {
  // Start tip rotation
  startTipRotation();
  
  // Start countdown timer
  startWaitingTimer();
  
  // Start auto-refresh
  startAutoRefresh();
  
  // Simulate processing completion after 3 minutes
  setTimeout(() => {
    completeProcessing();
  }, 180000); // 3 minutes
}

function startTipRotation() {
  setInterval(() => {
    tipIndex = (tipIndex + 1) % waitingTips.length;
    const tipElement = document.getElementById('waiting-tip');
    if (tipElement) {
      tipElement.textContent = waitingTips[tipIndex];
    }
  }, 10000); // Change tip every 10 seconds
}

function startWaitingTimer() {
  let timeLeft = 10; // 3 minutes in seconds
  
  waitingTimer = setInterval(() => {
    timeLeft--;
    const minutes = Math.floor(timeLeft / 60);
    const seconds = timeLeft % 60;
    
    const timeElement = document.getElementById('estimated-time');
    if (timeElement) {
      if (timeLeft > 60) {
        timeElement.textContent = `${minutes} menit ${seconds} detik`;
      } else if (timeLeft > 0) {
        timeElement.textContent = `${timeLeft} detik`;
      } else {
        timeElement.textContent = 'Hampir selesai...';
      }
    }
    
    if (timeLeft <= 0) {
      clearInterval(waitingTimer);
    }
  }, 1000);
}

function startAutoRefresh() {
  autoRefreshTimer = setInterval(() => {
    checkResults();
  }, 30000); // Check every 30 seconds
}

export function checkResults() {
  // Check if results are ready
  console.log('Checking assessment results...');

  const button = document.querySelector('button[onclick="checkResults()"]');
  if (button) {
    const originalText = button.textContent;
    button.textContent = 'Mengecek...';
    button.disabled = true;

    // Check if assessment results are available
    const resultsReady = localStorage.getItem('assessmentResultReady');
    const assessmentResults = localStorage.getItem('assessmentResults');
    const lastSaveResult = localStorage.getItem('lastSaveResult');

    setTimeout(() => {
      if (resultsReady && assessmentResults) {
        // Results are ready, navigate to results page
        navigateTo('result');
      } else {
        // Show status message
        let message = 'Hasil assessment masih diproses...';

        if (lastSaveResult) {
          const saveResult = JSON.parse(lastSaveResult);
          if (saveResult.fallbackSaved) {
            message = 'Assessment disimpan secara lokal. Mencoba sinkronisasi...';
          }
        }

        alert(message);
        button.textContent = originalText;
        button.disabled = false;
      }
    }, 1500);
  }
}

function completeProcessing() {
  // Clear timers
  if (waitingTimer) clearInterval(waitingTimer);
  if (autoRefreshTimer) clearInterval(autoRefreshTimer);
  
  // Mark assessment as completed
  localStorage.setItem('assessmentResultReady', 'true');
  
  // Show completion message and redirect
  alert('Assessment Anda telah selesai diproses! Anda akan diarahkan ke halaman hasil.');
  navigateTo('result');
}

export function cleanupWaiting() {
  if (waitingTimer) clearInterval(waitingTimer);
  if (autoRefreshTimer) clearInterval(autoRefreshTimer);
}
