export function createProfilePage() {
  return `
    <div class="min-h-screen bg-gray-50">
      <!-- Navigation -->
      <nav class="bg-white shadow">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="flex justify-between h-16">
            <div class="flex items-center">
              <button onclick="navigateTo('dashboard')" class="text-gray-500 hover:text-gray-700 mr-4">
                ← Dashboard
              </button>
              <h1 class="text-xl font-semibold text-gray-900">Profil Saya</h1>
            </div>
            <div class="flex items-center">
              <button onclick="saveProfile()" class="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700">
                Simpan <PERSON>
              </button>
            </div>
          </div>
        </div>
      </nav>

      <!-- Main Content -->
      <div class="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        <div class="space-y-6">
          <!-- Profile Header -->
          <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
              <h2 class="text-lg font-medium text-gray-900">Informasi Pribadi</h2>
            </div>
            <div class="px-6 py-4">
              <div class="flex items-center space-x-6">
                <div class="relative">
                  <div class="w-24 h-24 bg-gray-300 rounded-full flex items-center justify-center">
                    <span class="text-2xl text-gray-600" id="profile-avatar">👤</span>
                  </div>
                  <button onclick="changeAvatar()" 
                    class="absolute bottom-0 right-0 bg-indigo-600 text-white rounded-full w-8 h-8 flex items-center justify-center hover:bg-indigo-700">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"></path>
                    </svg>
                  </button>
                </div>
                <div class="flex-1">
                  <h3 class="text-xl font-semibold text-gray-900" id="profile-name">John Doe</h3>
                  <p class="text-gray-600" id="profile-email"><EMAIL></p>
                  <p class="text-sm text-gray-500">Member sejak: <span id="member-since">Januari 2024</span></p>
                </div>
              </div>
            </div>
          </div>

          <!-- Personal Information Form -->
          <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
              <h2 class="text-lg font-medium text-gray-900">Data Diri</h2>
            </div>
            <div class="px-6 py-4">
              <form id="profile-form" class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label for="firstName" class="block text-sm font-medium text-gray-700">
                      Nama Depan
                    </label>
                    <input type="text" id="firstName" name="firstName" value="John"
                      class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                  </div>
                  <div>
                    <label for="lastName" class="block text-sm font-medium text-gray-700">
                      Nama Belakang
                    </label>
                    <input type="text" id="lastName" name="lastName" value="Doe"
                      class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                  </div>
                </div>

                <div>
                  <label for="email" class="block text-sm font-medium text-gray-700">
                    Email
                  </label>
                  <input type="email" id="email" name="email" value="<EMAIL>"
                    class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label for="phone" class="block text-sm font-medium text-gray-700">
                      Nomor Telepon
                    </label>
                    <input type="tel" id="phone" name="phone" value="+62 812-3456-7890"
                      class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                  </div>
                  <div>
                    <label for="birthDate" class="block text-sm font-medium text-gray-700">
                      Tanggal Lahir
                    </label>
                    <input type="date" id="birthDate" name="birthDate" value="1990-01-01"
                      class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                  </div>
                </div>

                <div>
                  <label for="address" class="block text-sm font-medium text-gray-700">
                    Alamat
                  </label>
                  <textarea id="address" name="address" rows="3"
                    class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    placeholder="Masukkan alamat lengkap">Jl. Contoh No. 123, Jakarta Selatan</textarea>
                </div>
              </form>
            </div>
          </div>

          <!-- Professional Information -->
          <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
              <h2 class="text-lg font-medium text-gray-900">Informasi Profesional</h2>
            </div>
            <div class="px-6 py-4">
              <div class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label for="company" class="block text-sm font-medium text-gray-700">
                      Perusahaan
                    </label>
                    <input type="text" id="company" name="company" value="PT. Contoh Indonesia"
                      class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                  </div>
                  <div>
                    <label for="position" class="block text-sm font-medium text-gray-700">
                      Posisi
                    </label>
                    <input type="text" id="position" name="position" value="Senior Manager"
                      class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                  </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label for="industry" class="block text-sm font-medium text-gray-700">
                      Industri
                    </label>
                    <select id="industry" name="industry"
                      class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                      <option value="">Pilih Industri</option>
                      <option value="technology" selected>Teknologi</option>
                      <option value="finance">Keuangan</option>
                      <option value="healthcare">Kesehatan</option>
                      <option value="education">Pendidikan</option>
                      <option value="manufacturing">Manufaktur</option>
                      <option value="retail">Retail</option>
                      <option value="other">Lainnya</option>
                    </select>
                  </div>
                  <div>
                    <label for="experience" class="block text-sm font-medium text-gray-700">
                      Pengalaman Kerja
                    </label>
                    <select id="experience" name="experience"
                      class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                      <option value="">Pilih Pengalaman</option>
                      <option value="0-1">0-1 tahun</option>
                      <option value="1-3">1-3 tahun</option>
                      <option value="3-5" selected>3-5 tahun</option>
                      <option value="5-10">5-10 tahun</option>
                      <option value="10+">10+ tahun</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label for="skills" class="block text-sm font-medium text-gray-700">
                    Keahlian
                  </label>
                  <input type="text" id="skills" name="skills" 
                    value="Project Management, Strategic Planning, Team Leadership"
                    placeholder="Pisahkan dengan koma"
                    class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                  <p class="mt-1 text-sm text-gray-500">Pisahkan setiap keahlian dengan koma</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Account Settings -->
          <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
              <h2 class="text-lg font-medium text-gray-900">Pengaturan Akun</h2>
            </div>
            <div class="px-6 py-4">
              <div class="space-y-6">
                <div>
                  <label class="flex items-center">
                    <input type="checkbox" id="emailNotifications" checked
                      class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                    <span class="ml-2 text-sm text-gray-700">Terima notifikasi email</span>
                  </label>
                </div>
                <div>
                  <label class="flex items-center">
                    <input type="checkbox" id="profilePublic"
                      class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                    <span class="ml-2 text-sm text-gray-700">Buat profil dapat dilihat publik</span>
                  </label>
                </div>
                <div>
                  <label class="flex items-center">
                    <input type="checkbox" id="shareResults" checked
                      class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                    <span class="ml-2 text-sm text-gray-700">Izinkan berbagi hasil assessment dengan HR</span>
                  </label>
                </div>
              </div>
            </div>
          </div>

          <!-- Danger Zone -->
          <div class="bg-white shadow rounded-lg border border-red-200">
            <div class="px-6 py-4 border-b border-red-200">
              <h2 class="text-lg font-medium text-red-900">Zona Berbahaya</h2>
            </div>
            <div class="px-6 py-4">
              <div class="flex items-center justify-between">
                <div>
                  <h3 class="text-sm font-medium text-red-900">Hapus Akun</h3>
                  <p class="text-sm text-red-700">Tindakan ini tidak dapat dibatalkan</p>
                </div>
                <button onclick="deleteAccount()" 
                  class="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 text-sm">
                  Hapus Akun
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `;
}

export function initProfile() {
  // Load saved profile data
  loadProfileData();
}

function loadProfileData() {
  const savedProfile = localStorage.getItem('userProfile');
  if (savedProfile) {
    const profile = JSON.parse(savedProfile);
    
    // Populate form fields
    Object.keys(profile).forEach(key => {
      const element = document.getElementById(key);
      if (element) {
        if (element.type === 'checkbox') {
          element.checked = profile[key];
        } else {
          element.value = profile[key];
        }
      }
    });
    
    // Update profile display
    updateProfileDisplay(profile);
  }
}

function updateProfileDisplay(profile) {
  const nameElement = document.getElementById('profile-name');
  const emailElement = document.getElementById('profile-email');
  
  if (nameElement && profile.firstName && profile.lastName) {
    nameElement.textContent = `${profile.firstName} ${profile.lastName}`;
  }
  
  if (emailElement && profile.email) {
    emailElement.textContent = profile.email;
  }
}

export function saveProfile() {
  const form = document.getElementById('profile-form');
  const formData = new FormData(form);
  
  const profile = {};
  for (let [key, value] of formData.entries()) {
    profile[key] = value;
  }
  
  // Add checkbox values
  profile.emailNotifications = document.getElementById('emailNotifications').checked;
  profile.profilePublic = document.getElementById('profilePublic').checked;
  profile.shareResults = document.getElementById('shareResults').checked;
  
  // Save to localStorage
  localStorage.setItem('userProfile', JSON.stringify(profile));
  
  // Update display
  updateProfileDisplay(profile);
  
  // Show success message
  alert('Profil berhasil disimpan!');
}

export function changeAvatar() {
  // Simulate avatar change
  const avatars = ['👤', '👨', '👩', '🧑', '👨‍💼', '👩‍💼', '👨‍💻', '👩‍💻'];
  const currentAvatar = document.getElementById('profile-avatar');
  const randomAvatar = avatars[Math.floor(Math.random() * avatars.length)];
  
  if (currentAvatar) {
    currentAvatar.textContent = randomAvatar;
  }
}

export function deleteAccount() {
  const confirmation = prompt('Ketik "HAPUS" untuk mengkonfirmasi penghapusan akun:');
  
  if (confirmation === 'HAPUS') {
    if (confirm('Apakah Anda benar-benar yakin? Tindakan ini tidak dapat dibatalkan.')) {
      // Clear all data
      localStorage.clear();
      alert('Akun Anda telah dihapus. Anda akan diarahkan ke halaman login.');
      navigateTo('auth');
    }
  } else if (confirmation !== null) {
    alert('Konfirmasi tidak sesuai. Penghapusan akun dibatalkan.');
  }
}
